import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import <PERSON>HotEncoder
from lazypredict.Supervised import <PERSON><PERSON><PERSON><PERSON>ressor,LazyClassifier
from sklearn.metrics import mean_squared_error,mean_absolute_error
from sklearn.metrics import confusion_matrix
import seaborn as sns
import optuna as op
from mendeleev import element
import matplotlib.pyplot as plt
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.ensemble import ExtraTreesClassifier, ExtraTreesRegressor

idata=pd.read_csv('/home/<USER>/Coding/Machine learning on chalcogen perovskties/Final dataset for chalcogen perovskites.csv')
idata.fillna(0,inplace=True)
idata.drop('Unnamed: 0',axis=1,inplace=True)

def get_elem_attributes(elmnt):
    data_dict = {'Symbol':elmnt}
    elem = element(elmnt)
    props = {
        'Electronegativity': 'en_pauling',
        'Atomic radius': 'atomic_radius',
        'Group ID': 'group_id',
        'Electron affinity': 'electron_affinity'
    }
    for prop, att in props.items():
        data_dict[prop] = getattr(elem, att, 0)

    data_dict['Unpaired electrons'] = elem.ec.unpaired_electrons() if hasattr(elem, 'ec') and hasattr(elem.ec, 'unpaired_electrons') else np.nan
    return data_dict

get_elem_attributes('Bi')

idata

idata.loc[idata.B=='Na']

elements_list=set(idata.A.tolist()+idata.B.tolist())
element_datalist=[get_elem_attributes(x.strip()) for x in elements_list]

elements_df=pd.DataFrame(element_datalist)
elements_df.fillna(0)

A_data = idata[['A']].copy()
B_data = idata[['B']].copy()

elements_A = elements_df.add_suffix('_A').rename(columns={'Symbol_A': 'A'})
elements_B = elements_df.add_suffix('_B').rename(columns={'Symbol_B': 'B'})

A_data = A_data.join(elements_A.set_index('A'), on='A', how='left')
B_data = B_data.join(elements_B.set_index('B'), on='B', how='left')

fin_data = pd.concat([idata, A_data, B_data], axis=1)
fin_data.fillna(0,inplace=True)

fin_data.info()

encoder=OneHotEncoder()
values=encoder.fit_transform(fin_data[['Prototype']]).toarray()
encoded=pd.DataFrame(values,columns=encoder.get_feature_names_out())
fin_data=fin_data.join(encoded)
fin_data=fin_data.select_dtypes(include=np.number)

fin_data

num_data=fin_data.select_dtypes(include=np.number)

fin_data

idata.Prototype.value_counts()

num_data

X_data=num_data.drop('Bandgap',axis=1)
y_data=num_data.Bandgap
X_train,X_test,y_train,y_test=train_test_split(X_data,y_data,test_size=0.15,shuffle=True,random_state=42)

lazy_reg=LazyRegressor(verbose=1,ignore_warnings=True)
models,predictions=lazy_reg.fit(X_train,X_test,y_train,y_test)

print(predictions)

lazy_class=LazyClassifier(verbose=1,ignore_warnings=True)
class_results=lazy_class.fit(X_train,X_test,y_train>0,y_test>0)

class_results[0]

class Combined_Model(BaseEstimator, TransformerMixin):
    def __init__(self,class_model,reg_model):
        self.class_model=class_model
        self.reg_model=reg_model

    def fit(self,X_train,y_train):
       print('Initializng model training\n#############################\n')
       print('\n Classification model: ',self.class_model.__class__.__name__)
       print('\n Classification model: ',self.reg_model.__class__.__name__)
       self.class_model.fit(X_train,y_train>0)
       self.reg_model.fit(X_train,y_train)

    def predict(self,X_test):
        mask=self.class_model.predict(X_test)
        pred=self.reg_model.predict(X_test)*mask
        self._preds=pred.astype(float)
        return self._preds
            
    def evaluate(self, y_test):

        print(y_test.shape)
        mse=mean_squared_error(y_test,self._preds)
        mae=mean_absolute_error(y_test,self._preds)


        cm = confusion_matrix(y_test > 0, self.class_model.predict(X_test))
        print(cm)

        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        axes[0].scatter(y_test, self._preds, alpha=0.7, edgecolors='k', color='#1f77b4')
        axes[0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0].set_xlabel('True Values')
        axes[0].set_ylabel('Predictions')
        axes[0].set_title('Regression: Predictions vs True')
        axes[0].grid(True, linestyle='--', alpha=0.5)

        textstr = f'RMSE: {np.sqrt(mse):.4f}\nMAE: {mae:.4f}'
        props = dict(boxstyle='round', facecolor='white', alpha=0.8)
        axes[0].text(0.05, 0.95, textstr, transform=axes[0].transAxes, fontsize=12,
                    verticalalignment='top', bbox=props)

        # Confusion matrix heatmap
        sns.heatmap(cm, annot=True, fmt='f', cmap='Blues', cbar=False, ax=axes[1])
        axes[1].set_xlabel('Predicted')
        axes[1].set_ylabel('True')
        axes[1].set_title('Classification: Confusion Matrix')

        plt.tight_layout()
        plt.show()

print(my_model._preds.dtype)

my_model=Combined_Model(ExtraTreesClassifier(),ExtraTreesRegressor())

my_model.fit(X_train,y_train)

my_model.predict(X_test)
my_model.evaluate(y_test)

